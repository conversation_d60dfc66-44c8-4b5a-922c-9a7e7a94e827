import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Star,
  MapPin,
  Clock,
  Phone,
  Calendar,
  User,
  Activity,
  Eye,
  Trash2,
  RefreshCw,
  Download,
} from "lucide-react";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

// Sample patient data
const patientsData = [
  {
    id: "PT001",
    name: "<PERSON><PERSON> <PERSON>",
    age: 32,
    gender: "F",
    appointmentDate: "2024-07-20",
    contact: "+91 **********",
    healthConcern: "Erectile Dysfunction",
    describeConcern:
      "I have been experiencing severe stomach pain and bloating after meals for the past two weeks. The pain is particularly intense in the evening and affects my sleep quality.",
    prescriptionGiven: "Yes",
    status: "Completed",
  },
  {
    id: "PT002",
    name: "Arjun Verma",
    age: 45,
    gender: "M",
    appointmentDate: "2024-07-20",
    contact: "+91 **********",
    healthConcern: "Premature Ejaculation",
    describeConcern:
      "My knee joints have been aching for the past month, especially when I climb stairs or sit for long periods. The pain is worse in the morning.",
    prescriptionGiven: "Yes",
    status: "Completed",
  },
  {
    id: "PT003",
    name: "Radha Krishnan",
    age: 28,
    gender: "F",
    appointmentDate: "2024-07-21",
    contact: "+91 **********",
    healthConcern: "Low Libido",
    prescriptionGiven: "No",
    status: "Pending",
  },
  {
    id: "PT004",
    name: "Vikram Singh",
    age: 38,
    gender: "M",
    appointmentDate: "2024-07-21",
    contact: "+91 **********",
    healthConcern: "Infertility",
    describeConcern:
      "I have developed a rash on my arms and face that has been persistent for over a week. It's itchy and seems to worsen with exposure to sunlight.",
    prescriptionGiven: "Yes",
    status: "Follow-up",
  },
  {
    id: "PT005",
    name: "Meera Patel",
    age: 55,
    gender: "F",
    appointmentDate: "2024-07-22",
    contact: "+91 **********",
    healthConcern: "Hormonal Imbalance",
    describeConcern:
      "My blood sugar levels have been fluctuating despite following my prescribed diet. I need guidance on better management techniques.",
    prescriptionGiven: "Yes",
    status: "Pending",
  },
];

const Consultant = () => {
  const { toast } = useToast();
  const [patients, setPatients] = useState(patientsData);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [followUpFilter, setFollowUpFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Filter patients based on search and filters
  const filteredPatients = useMemo(() => {
    return patients.filter((patient) => {
      const matchesSearch =
        patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patient.contact.includes(searchTerm);
      const matchesStatus =
        statusFilter === "all" ||
        patient.status.toLowerCase() === statusFilter.toLowerCase();
      const matchesDate =
        dateFilter === "all" || patient.appointmentDate === dateFilter;
      const matchesFollowUp =
        followUpFilter === "all" ||
        (followUpFilter === "due" && patient.status === "Follow-up");
      const matchesCategory =
        categoryFilter === "all" || patient.healthConcern === categoryFilter;

      return (
        matchesSearch &&
        matchesStatus &&
        matchesDate &&
        matchesFollowUp &&
        matchesCategory
      );
    });
  }, [
    patients,
    searchTerm,
    statusFilter,
    dateFilter,
    followUpFilter,
    categoryFilter,
  ]);

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedPatients,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredPatients,
    itemsPerPage: 10,
  });

  // Calculate dynamic stats
  const consultantStats = useMemo(() => {
    const totalPatients = patients.length;
    const appointmentsToday = patients.filter(
      (p) => p.appointmentDate === "2024-07-20"
    ).length;
    const followUpsScheduled = patients.filter(
      (p) => p.status === "Follow-up"
    ).length;
    const completedConsultations = patients.filter(
      (p) => p.status === "Completed"
    ).length;

    return [
      {
        title: "Total Patients",
        value: "250",
        color: "text-gray-900",
        icon: User,
      },
      {
        title: "Appointments Today",
        value: "15",
        color: "text-blue-600",
        icon: Calendar,
      },
      {
        title: "Follow-ups Scheduled",
        value: "8",
        color: "text-orange-600",
        icon: Activity,
      },
      {
        title: "Total Consultations Completed",
        value: "200",
        color: "text-green-600",
        icon: Activity,
      },
    ];
  }, [patients]);

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter("all");
    setFollowUpFilter("all");
    setCategoryFilter("all");
  };

  const handleViewPatient = (patient) => {
    setSelectedPatient(patient);
    setIsViewModalOpen(true);
  };

  const handleDeletePatient = (patientId) => {
    setPatients((prev) => prev.filter((p) => p.id !== patientId));
    toast({
      title: "Patient Deleted",
      description: "Patient record has been successfully deleted.",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Completed:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      Pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      "Follow-up":
        "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    };
    return (
      statusConfig[status] ||
      "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    );
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Refresh patient data (in real app, this would fetch from API)
    setPatients([...patientsData]);
    setIsRefreshing(false);

    toast({
      title: "Consultant Data Refreshed",
      description: "Patient data has been successfully updated",
    });
  };

  const handleExportToCSV = async () => {
    setIsExporting(true);

    try {
      // Prepare CSV data
      const csvHeaders = [
        "Patient Name",
        "Contact",
        "Age",
        "Gender",
        "Appointment Date",
        "Health Concern",
        "Prescription Given",
        "Status",
      ];

      const csvData = filteredPatients.map((patient) => [
        patient.name,
        patient.contact,
        patient.age,
        patient.gender,
        patient.appointmentDate,
        patient.healthConcern,
        patient.prescriptionGiven,
        patient.status,
      ]);

      // Create CSV content
      const csvContent = [
        csvHeaders.join(","),
        ...csvData.map((row) => row.map((cell) => `"${cell}"`).join(",")),
      ].join("\n");

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `consultant_patients_export_${
          new Date().toISOString().split("T")[0]
        }.csv`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Export Successful",
        description: "Patient data has been exported to CSV",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export patient data",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Consultant
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage patient interactions and consultation tracking for Dr. Kumar
            Clinic.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
          <Button
            variant="outline"
            onClick={handleExportToCSV}
            disabled={isExporting}
            className="text-base px-6 py-3"
          >
            <Download className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
        </div>
      </div>

      {/* Clinic Details Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
            {/* Clinic Logo */}
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center shadow-lg shrink-0">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-700 rounded-full flex items-center justify-center">
                <div className="text-white text-2xl">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="text-white"
                  >
                    <path
                      d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                      fill="currentColor"
                    />
                    <path
                      d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12Z"
                      fill="currentColor"
                      opacity="0.7"
                    />
                    <path
                      d="M12 14C8.69 14 6 16.69 6 20H18C18 16.69 15.31 14 12 14Z"
                      fill="currentColor"
                      opacity="0.5"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Clinic Information */}
            <div className="flex-1 text-center sm:text-left">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Dr. Kumar Clinic
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-3">
                Ayurvedic Consultant
              </p>
              <div className="flex flex-wrap items-center justify-center sm:justify-start gap-4 text-sm text-gray-500 dark:text-gray-400">
                <span>5 years experience</span>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>4.4 rating</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>Gwalior, India</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>Available: Mon-Fri, 9 AM - 6 PM</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {consultantStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <AnimatedNumber
                    value={stat.value}
                    className="text-3xl font-bold"
                  />
                </div>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Patient Management Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Patients
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Patients
                </label>
                <Input
                  placeholder="Search by name or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Appointment Date
                </label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Filter by date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="2024-07-20">Today</SelectItem>
                    <SelectItem value="2024-07-21">Tomorrow</SelectItem>
                    <SelectItem value="2024-07-22">Day After</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="follow-up">Follow-up</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Follow-up Due
                </label>
                <Select
                  value={followUpFilter}
                  onValueChange={setFollowUpFilter}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Filter follow-ups" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Patients</SelectItem>
                    <SelectItem value="due">Follow-up Due</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Health Category
                </label>
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="Erectile Dysfunction">
                      Erectile Dysfunction
                    </SelectItem>
                    <SelectItem value="Premature Ejaculation">
                      Premature Ejaculation
                    </SelectItem>
                    <SelectItem value="Low Libido">Low Libido</SelectItem>
                    <SelectItem value="Infertility">Infertility</SelectItem>
                    <SelectItem value="Hormonal Imbalance">
                      Hormonal Imbalance
                    </SelectItem>
                    <SelectItem value="Penis Enlargement">
                      Penis Enlargement
                    </SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing {paginatedPatients.length} of {filteredPatients.length}{" "}
              filtered patients ({patients.length} total)
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Patient Management Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Patient Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Patient ID</div>
              <div className="text-center">Name</div>
              <div className="text-center">Age / Gender</div>
              <div className="text-center">Appointment Date</div>
              <div className="text-center">Contact</div>
              <div className="text-center">Health Concern</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {paginatedPatients.map((patient, index) => (
                <div
                  key={index}
                  className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                >
                  {/* Patient ID */}
                  <div className="text-center">
                    <AnimatedText className="font-medium text-lg text-blue-600 dark:text-blue-400">
                      {patient.id}
                    </AnimatedText>
                  </div>

                  {/* Name */}
                  <div className="text-center">
                    <AnimatedText className="font-medium text-lg text-gray-900 dark:text-white">
                      {patient.name}
                    </AnimatedText>
                  </div>

                  {/* Age/Gender */}
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.age}/{patient.gender}
                    </span>
                  </div>

                  {/* Appointment Date */}
                  <div className="text-center">
                    <span className="font-medium text-lg text-gray-900 dark:text-white">
                      {patient.appointmentDate}
                    </span>
                  </div>

                  {/* Contact */}
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.contact}
                    </span>
                  </div>

                  {/* Health Concern */}
                  <div className="text-center">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      {patient.healthConcern}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewPatient(patient)}
                      className="p-2"
                      title="View Patient Details"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeletePatient(patient.id)}
                      className="p-2 text-red-600 hover:text-red-700"
                      title="Delete Patient"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              totalItems={totalItems}
              itemsPerPage={10}
            />

            {filteredPatients.length === 0 && (
              <div className="text-center py-8">
                <p className="text-lg text-gray-500 dark:text-gray-400">
                  No patients found matching your search criteria.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Patient View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Patient Details</DialogTitle>
          </DialogHeader>
          {selectedPatient && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Patient ID:
                  </label>
                  <p className="text-blue-600 dark:text-blue-400">
                    {selectedPatient.id}
                  </p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Name:
                  </label>
                  <p>{selectedPatient.name}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Age:
                  </label>
                  <p>{selectedPatient.age}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Gender:
                  </label>
                  <p>{selectedPatient.gender}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Contact:
                  </label>
                  <p>{selectedPatient.contact}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Appointment Date:
                  </label>
                  <p>{selectedPatient.appointmentDate}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    Health Concern:
                  </label>
                  <p>{selectedPatient.healthConcern}</p>
                </div>
                {selectedPatient.describeConcern && (
                  <div className="col-span-2">
                    <label className="font-medium text-gray-700 dark:text-gray-300">
                      Describe Your Concern:
                    </label>
                    <p className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-800 dark:text-gray-200 leading-relaxed">
                      {selectedPatient.describeConcern}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Consultant;
