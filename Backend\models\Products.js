const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        default: ''
    },
    active:{
        type: Boolean,
        default: true
    },
    categoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Category',
        required: true
    },
    price: {
        type: Number,
        required: true,
        min: 0
    },
    category: {
        type: String,
        default: ''
    },
    inStock: {
        type: Boolean,
        default: true
    },
    reviews:[
       {
        _id: {
                type: mongoose.Schema.Types.ObjectId,
                auto: true
         },
          user:{    
              type: mongoose.Schema.Types.ObjectId,
              ref: 'User',
              required: true
          },
            rating: {
                type: Number,
                required: true,
                min: 0,
                max: 5
            },
            comment: {
                type: String,
                default: ''
            },
       }
    ],
    totalReviews: {
        type: Number,   
        default: 0
    },
    averageRating: {
        type: Number,
        default: 0,
        min: 0,
        max: 5
    },
    images: [   
        {
            url: {
                type: String,
                required: true
            },
            altText: {
                type: String,
                default: ''
            }
        }
    ],
    isFeatured: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

productSchema.pre('save', function (next) {
    if (!this.isNew) {
        this.updatedAt = Date.now();
    }
    next();
});

productSchema.pre('findOneAndUpdate', function (next) {
    this.set({ updatedAt: Date.now() });
    next();
});

productSchema.pre('updateOne', function (next) {
    this.set({ updatedAt: Date.now() });
    next();
});
productSchema.methods.updateReviewStats = async function () {
    const reviews = this.reviews || [];
    const totalReviews = reviews.length;
    let averageRating = 0;
    if (totalReviews > 0) {
        if (totalReviews === 1) {
            averageRating = reviews[0].rating || 0;
        } else {
            // Assume the last review is the new one
            const oldAverage = this.averageRating || 0;
            const newRating = reviews[totalReviews - 1].rating || 0;
            averageRating = ((oldAverage * (totalReviews - 1)) + newRating) / totalReviews;
        }
    }
    this.totalReviews = totalReviews;
    this.averageRating = averageRating;
    await this.save();
};
module.exports = mongoose.model('Product', productSchema);