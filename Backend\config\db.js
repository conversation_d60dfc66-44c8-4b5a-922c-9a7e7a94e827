const mongoose = require('mongoose');

const connectDB =  () =>{
    const dbUrl = process.env.DB_URL;

   const connection =  mongoose.connect(dbUrl, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        dbName: process.env.DB_NAME || 'ecommerce'
    })
    .then(() => console.log('MongoDB connected successfully'))
    .catch(err => console.error('MongoDB connection error:', err));

    return connection;
}

module.exports = connectDB;
