const express = require('express');
const router = express.Router();
const {
    registerUser,
    loginUser,
    verifyEmail,
    getUserProfile,
    updateUserProfile,
    changePassword,
    forgotPassword,
    resetPassword
} = require('../../controllers/userController');
const { protect } = require('../../middleware/auth');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

// Public routes
// @route   POST /api/users/register
// @desc    Register new user
// @access  Public
router.post('/register', registerUser);

// @route   POST /api/users/login
// @desc    Login user
// @access  Public
router.post('/login', loginUser);

// @route   GET /api/users/verify-email/:token
// @desc    Verify email
// @access  Public
router.get('/verify-email/:token', verifyEmail);

// @route   POST /api/users/forgot-password
// @desc    Forgot password
// @access  Public
router.post('/forgot-password', forgotPassword);

// @route   POST /api/users/reset-password/:token
// @desc    Reset password
// @access  Public
router.post('/reset-password/:token', resetPassword);

// Private routes (require authentication)
router.use(protect);

// @route   GET /api/user/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', require('../../controllers/userController').getUserProfile);

// @route   PUT /api/user/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', upload.single('profileImage'), require('../../controllers/userController').updateUserProfile);

// @route   PUT /api/user/users/change-password
// @desc    Change password
// @access  Private
router.put('/change-password', require('../../controllers/userController').changePassword);

module.exports = router;
